<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HesYearHAreaMapper">

    <!-- 获取换热站面积 -->
    <select id="getHareaByDate" resultType="string">
        select  heara from T_Hes_Year_HArea where hescode =#{hescode} and hyear=#{hyear}   and hmonth &lt;=#{hmonth} order by id desc limit 0,1 ;
    </select>


    <select id="getHareaByYear" resultType="string">
        select  heara from T_Hes_Year_HArea where hescode =#{hescode} and hyear=#{hyear}  order by id desc limit 0,1 ;
    </select>

    <!-- 检查指定换热站和年份的记录是否存在 -->
    <select id="checkRecordExists" resultType="Integer">
        SELECT COUNT(id) FROM t_hes_year_harea
        WHERE hescode = #{hescode} AND hyear = #{hyear}
    </select>

    <!-- 插入换热站年度供热面积记录 -->
    <insert id="insertHesYearHArea" parameterType="com.javaweb.system.entity.HesYearHArea">
        INSERT INTO t_hes_year_harea (hesname, hescode, hyear, heara, freearea)
        VALUES (#{hesname}, #{hescode}, #{hyear}, #{heara}, #{freearea})
    </insert>

    <!-- 更新换热站年度供热面积记录 -->
    <update id="updateHesYearHArea">
        UPDATE t_hes_year_harea
        SET heara = #{heara},hesname=#{hesname},freearea = #{freearea}
        WHERE hescode = #{hescode} AND hyear = #{hyear}
    </update>

</mapper>