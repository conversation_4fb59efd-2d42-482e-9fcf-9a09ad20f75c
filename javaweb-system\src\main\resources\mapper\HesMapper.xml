<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HesMapper">

    <!-- 获取换热站信息 -->
    <select id="getHesUsed" resultType="com.javaweb.system.entity.Hes">
        SELECT hescode,Name,heat_unit_name,heat_rate,heating_index FROM t_hes where is_used=1;
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesName" resultType="String">
        SELECT Name FROM t_hes where hescode=#{hescode};
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesByNo" resultType="com.javaweb.system.entity.Hes">
        SELECT hescode,Name,heat_unit_name,heat_rate,heating_index FROM t_hes where hescode=#{hescode};
    </select>


    <!-- 获取换热站信息 -->
    <select id="getHesDtoUsed" resultType="com.javaweb.system.dto.HesDto">
        SELECT  id,hescode,Name FROM t_hes where is_used=1;
    </select>


    <!-- 获取换热站信息 -->
    <select id="getHesList" resultType="com.javaweb.system.dto.HesDto">
        SELECT  id,hescode,Name,is_used,is_run FROM t_hes ;
    </select>    <!-- 获取换热站信息 -->

    <select id="getHesEnergy" resultType="com.javaweb.system.dto.HesEnergyDto">
        SELECT  dt, F_S_C_H,avgT,area,energy from t_hes_statistics_day where hescode=#{hescode} and dt &lt;= #{enddt}  and  dt>= #{startdt}
       order by id desc;
    </select>

    <select id="getHesHourEnergy" resultType="com.javaweb.system.dto.HesEnergyHourDto">
        SELECT  * from t_hes_statistics_hour where dt = #{startdt}  and hescode in
        <foreach collection="hescodes" item="item" index="index" open="(" separator="," close=")" >
            (#{item})
        </foreach>
    </select>

    <!-- 获取换热站信息 -->
    <select id="selectByCode" resultType="com.javaweb.system.entity.Hes">
        SELECT hescode,Name FROM t_hes where hescode=#{hescode};
    </select>


    <!-- 获取换热站最大的编号 -->
    <select id="getHesMaxCode" resultType="Integer">
        SELECT  max(hescode) as hescode FROM t_hes;
    </select>

    <!-- 获取所有换热站信息（包含关联的小区ID） -->
    <select id="getAllHesWithHeatUnitId" resultType="com.javaweb.system.entity.Hes">
        SELECT id, hescode, name, heat_unit_id
        FROM t_hes
        WHERE heat_unit_id IS NOT NULL
        ORDER BY hescode ASC
    </select>

    <select id="getHesCodesByHeatunitId" resultType="java.lang.Integer">
        SELECT  hescode from t_hes where heat_unit_id in
        <foreach collection="heatUnitIds" item="item" index="index" open="(" separator="," close=")" >
            (#{item})
        </foreach>
    </select>


</mapper>
