package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.HesYearHArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 增删改查
 *
 * @Description :
 * <AUTHOR> mc
 * @Date: 2022/12/6 15:15
 */
public interface HesYearHAreaMapper extends BaseMapper<HesYearHArea> {



    List<HesYearHArea> getList();

    /*
    * 获得换热站的面积
    *
    * */
    String getHareaByDate(int hescode,int hyear,int hmonth);

    /*
     * 获得换热站的年面积
     *
     * */
    String getHareaByYear(int hescode,int hyear);

    /**
     * 检查指定换热站和年份的记录是否存在
     *
     * @param hescode 换热站编号
     * @param hyear 年份
     * @return 记录数量，大于0表示存在
     */
    Integer checkRecordExists(@Param("hescode") Integer hescode, @Param("hyear") Integer hyear);

    /**
     * 插入换热站年度供热面积记录
     *
     * @param hesYearHArea 换热站年度供热面积实体
     */
    void insertHesYearHArea(HesYearHArea hesYearHArea);

    /**
     * 更新换热站年度供热面积记录
     *
     * @param hescode 换热站编号
     * @param hyear 年份
     * @param heara 供热面积
     * @param freearea 未供热面积
     */
    void updateHesYearHArea(@Param("hescode") Integer hescode,
                            @Param("hesname") String  hesname,
                            @Param("hyear") Integer hyear,
                            @Param("heara") String heara,
                            @Param("freearea") String freearea);
}

