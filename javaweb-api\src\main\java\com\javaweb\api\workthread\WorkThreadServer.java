package com.javaweb.api.workthread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.javaweb.api.entity.*;
import com.javaweb.api.mapper.*;
import com.javaweb.api.utils.CalcUtils;
import com.javaweb.api.utils.ProjectUtils;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import io.lettuce.core.RedisException;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;


import javax.sql.DataSource;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;
import java.util.concurrent.Executors;

@Configuration
@EnableScheduling
public class WorkThreadServer {
    private static final Logger logger = LoggerFactory.getLogger(WorkThreadServer.class);
    
    @Autowired
    DataSource dataSource;

    @Value("${weatherServer.ip}")
    private String sIP;

    @Value("${weatherServer.port}")
    private int nPort;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    @Qualifier("stringRedisTemplateForObject")
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    HouseInfoMapper houseInfoMapper;

    @Autowired
    HeatUnitMapper heatunitMapper;

    List<HeatUnit> heatUnitlst=new ArrayList<>();

    //定义一个类级变量用于存储键值对   阀门采集周期
    private Map<String, Integer> valvesCycleMap = new HashMap<>();

    //热表采集周期
    private Map<String, Integer> heatMaterCycleMap= new HashMap<>();

    /*
     * 一启动就获取气象温度
     *
     * */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay = 10000, fixedDelay = 900000)
    public void refreshHouseAndWeatherData() {
        // 从小区信息表中提取所有不同的城市名字
        Set<String> cities = new HashSet<>();
        for (HeatUnit heatUnit : heatUnitlst)
        {
            cities.add(heatUnit.getCityName()); // 假设HeatUnit类有getCityName()方法
        }
        // 为每个城市获取气象数据
        for (String city : cities) {
            // 使用正则表达式去除单独的 "市" 和 "县"
            city = city.replaceAll("(市$)|(县$)", ""); // $ 表示匹配字符串的结尾
//            System.out.println(city);
            getWeatherDataForCity(city);
        }
    }


    public void getWeatherDataForCity(String city) {

        try {
            String url = "http://"+sIP+":"+nPort+"/api/weather?city="+city;
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpGet request = new HttpGet(url);
            HttpResponse response = httpClient.execute(request);
            // 解析响应内容
            String jsonString = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(jsonString);

            // 验证数据
            if (jsonObject.getIntValue("code") == 0) {
                // 成功获取数据，插入数据库
                JSONObject data = jsonObject.getJSONObject("data");
                city = data.getString("city");
                double temperature = data.getDoubleValue("temperature");
                double humidity = data.getDoubleValue("humidity");
                double windSpeed = data.getDoubleValue("wind_speed");
                double feelsLike = data.getDoubleValue("feels_like");
                double solarRadiation = data.getDoubleValue("solar_radiation");
                String observationTime = data.getString("observation_time");

                try (Connection conn = dataSource.getConnection()) {
                    String tablename = "t_weather_station_data";
                    // 插入数据
                    String sql = "INSERT INTO " + tablename + " (ws, T, H, S, F, R, CollectDt) VALUES (?, ?, ?, ?, ?, ?, ?)";
                    try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                        stmt.setString(1, city);
                        stmt.setDouble(2, temperature);
                        stmt.setDouble(3, humidity);
                        stmt.setDouble(4, windSpeed);
                        stmt.setDouble(5, feelsLike);
                        stmt.setDouble(6, solarRadiation);
                        stmt.setString(7, observationTime);
                        int num = stmt.executeUpdate();
                        if (num > 0) {
                            System.out.println("气象数据插入成功");
                        } else {
                            System.out.println("数据插入失败");
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                // 错误处理
                System.out.println("错误消息: " + jsonObject.getString("message"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /*
    * 一启动就读取数据库中所有的小区信息
    *
    * */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay = 1000,fixedDelay=180000)
    public void getHouseInfo()
    {
        //获取所有的小区数据
        heatUnitlst=new ArrayList<>();
        String hashname="JM:HeatUnitlist";// 创建Gson对象
        Gson gson = new Gson();
        Map<Object, Object> lst = redisUtils.hmget(hashname);
        // 遍历 Map
        for (Map.Entry<Object, Object> entry : lst.entrySet()) {
            Object value = entry.getValue();
            // 处理键和值
            //System.out.println(" Value: " + value);
            // 现在将JSON字符串转换回对象
            HeatUnit heatunit = gson.fromJson(value.toString(), HeatUnit.class);
            heatUnitlst.add(heatunit);
        }
//        List<HouseInfo> houseInfolst=new ArrayList<>();
//        // 获取所有住户信息
//         houseInfolst=houseInfoMapper.getHouseBaseinfo();
//         //快速查找给定集中器Id得到对应的小区编号
//         for (HouseInfo info : houseInfolst) {
//             unitnoMap.put(info.getConcentratorId(), info.getUnitNo());
//         }
//         heatUnitlst=heatunitMapper.getHeatUnitBaseinfo();
    }

    /*
    *  从redis 中获取设备的采集周期   半小时获取一次
    *
    * */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay = 5000,fixedDelay=180000)
    public void getDataCycle()
    {
        String key = "JM:DataCycle";
        Map<Object, Object> resultMap = getRedisData(key, 100);
        if (resultMap == null || resultMap.isEmpty()) {
            return; // 没有数据就直接返回
        }
        for (Map.Entry<Object, Object> entry : resultMap.entrySet())
        {
            try {
                // 安全获取键和值
                String code = convertToString(entry.getKey());
                String value = convertToString(entry.getValue());

                if (code == null || value == null) {
                    continue; // 跳过无效条目
                }

                String[] parts = value.split(",");
                if (parts.length < 3) {
                    // 数据格式不完整，跳过
                    continue;
                }
                //判断是否供暖季（1=供暖季，2=非供暖季）
                LocalDate today = LocalDate.now();
                Boolean heatingPeriod = ProjectUtils.isHeatingSeason(today) ? true : false;
                int cycle=30;// 默认周期
                if (heatingPeriod) {
                    cycle = parseInteger(parts[1], 30); // 如果解析失败则使用默认值
                } else {
                    cycle = parseInteger(parts[2], 30);
                }

                //1为阀门
                if(parts[0].equals("1"))
                {
                    valvesCycleMap.put(code,cycle);
                }else if(parts[0].equals("3"))//热表
                {
                    heatMaterCycleMap.put(code,cycle);
                }
            } catch (Exception e) {
                // 可以记录日志
                System.err.println("处理 Redis 数据失败: " + e.getMessage());
            }
        }

    }
    // 将任意对象转为 String
    private String convertToString(Object obj) {
        if (obj == null) return null;
        return obj.toString();
    }

    // 安全地将字符串转为整数，默认 fallbackValue
    private int parseInteger(String str, int fallbackValue) {
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return fallbackValue;
        }
    }
    //redis 中获得设备数据
    public Map<Object, Object> getRedisData(String key,Integer batchSize)
    {
        //获取阀门数据
        Map<Object, Object> result = new HashMap<>();
        Cursor<Map.Entry<Object, Object>> cursor = null;
        int count=0;
        try {
            // 使用ScanOptions来指定每次迭代返回的元素数量
            ScanOptions options = ScanOptions.scanOptions().count(batchSize).match("*").build();
            cursor = redisTemplate.opsForHash().scan(key, options);
            while (cursor.hasNext())
            {
                count++;
                Map.Entry<Object, Object> entry = cursor.next();
                result.put(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            // 处理异常，例如打印日志或重新抛出异常
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    // 处理关闭游标时可能发生的IOException
                    e.printStackTrace();
                    System.out.println("游标关闭错误信息: " + e.getMessage());
                }
            }
        }
        return result;
    }

    /*
     *
     * 从指定redis 中解析数据为json 格式的字符串,将这些数据插入到对应的数据表中
     * 1，解析数据为json
     * 2.根据解析的数据中的阀门id 查找对应的小区编号，以此获取到表名  t_valves_data_小区编号
     * 3.批量插入数据到数据库中
     *
     * */
    @Async("threadPoolTaskExecutor")  //10分钟执行一次
    @Scheduled(initialDelay = 20000, fixedDelay = 600000)
    public void doSomething() {
        long startTime = System.currentTimeMillis();
        System.out.println("【数据处理】开始执行数据处理任务: " + getCurrentDateTime());
        
        String key = "TLGW100V10:DATA";
        int batchSize = 1000;
        Set<Object> itemsToRemove = new HashSet<>();
        ScanOptions options = ScanOptions.scanOptions().count(batchSize).build();
        
        System.out.println("【数据处理】正在从Redis中扫描数据集: " + key);
        Cursor<Object> cursor = redisTemplate.opsForSet().scan(key, options);
        
        List<HValvesData> valveDataList = new ArrayList<>();
        List<HeatMaterData> heatMeterDataList = new ArrayList<>();
        int cycle =30;
//        System.out.println("【数据处理】正在获取采集周期参数...");
//        long cycleStartTime = System.currentTimeMillis();
//        int cycle = getCycleByDataBase();
//        long cycleEndTime = System.currentTimeMillis();
//        System.out.println("【数据处理】采集周期获取完成，周期值: " + cycle + "，耗时: " + (cycleEndTime - cycleStartTime) + "ms");
//
        int totalItems = 0;
        int validItems = 0;
        int errorItems = 0;
        int valvesItems = 0;
        int heatMeterItems = 0;
        
        System.out.println("【数据处理】开始解析Redis数据...");
        long parseStartTime = System.currentTimeMillis();
        try {
            while (cursor.hasNext()) {
                totalItems++;
                Object item = cursor.next();
                itemsToRemove.add(item); // 添加到待删除集合中
                
                // 处理每个元素
                String itemString = item.toString();
                String[] splitItems = itemString.split(";");
                String type = "Error";
                
                if (splitItems.length >= 3) {
                    validItems++;
                    for (int i = 2; i < splitItems.length; i++) {
                        String[] splitDatas = splitItems[i].split(",");
                        if (splitDatas.length > 6) {
                            // 处理数据
                            String concentratorId = splitItems[0];//集中器id
                            String valvesno = splitDatas[3];//仪表id
                            if (Integer.parseInt(splitDatas[1]) == 210) {
                                type = "Valves";
                                valvesItems++;
                                //获取存储的采集周期
                                //cycle= valvesCycleMap.get(concentratorId);
                                cycle=30;
                                //System.out.println("【阀门】采集周期获取完成，周期值: " + cycle);
                                HValvesData data = parseHValvesData(concentratorId, valvesno, splitDatas[6], cycle);
                                if (data != null) {
                                    valveDataList.add(data);
                                } else {
                                    // 阀门数据解析失败，记录错误信息
                                    System.out.println("【数据处理】阀门数据解析失败，该条数据将被删除 - 集中器ID: " +
                                                     concentratorId + ", 阀门号: " + valvesno +
                                                     ", 原始数据项: " + itemString);
                                    type = "InvalidValvesData";
                                    errorItems++;
                                }
                            } else if (Integer.parseInt(splitDatas[1]) == 3) {
                                type = "HeatMeter";
//                                cycle=heatMaterCycleMap.get(concentratorId);
                                cycle=30;
                                //System.out.println("【热表】采集周期获取完成，周期值: " + cycle);
                                heatMeterItems++;
                                HeatMaterData data = parseHeatData(concentratorId, valvesno, splitDatas[6], cycle);
                                if (data != null) {
                                    heatMeterDataList.add(data);
                                }
                            }
                        } else {
                            type = "ErrorData";
                            errorItems++;
                        }
                        insertLog(type, itemString);
                    }
                } else {
                    errorItems++;
                    insertLog(type, itemString);
                }
            }
            
            long parseEndTime = System.currentTimeMillis();
            System.out.println("【数据处理】数据解析完成，总计: " + totalItems + "项，有效: " + validItems + 
                    "项，错误: " + errorItems + "项，阀门数据: " + valvesItems + "项，热表数据: " + heatMeterItems + 
                    "项，解析耗时: " + (parseEndTime - parseStartTime) + "ms");
            
            // 批量插入数据到数据库
            if (!valveDataList.isEmpty()) {
                //System.out.println("【数据处理】开始批量插入阀门数据，数量: " + valveDataList.size());
                long valveInsertStartTime = System.currentTimeMillis();
                batchInsertData(valveDataList);
                long valveInsertEndTime = System.currentTimeMillis();
                System.out.println("【数据处理】阀门数据插入完成，耗时: " + (valveInsertEndTime - valveInsertStartTime) + "ms");
                long valveUpdateStartTime = System.currentTimeMillis();
                batchUpdateData(valveDataList);
                long valveUpdateEndTime = System.currentTimeMillis();
                System.out.println("【数据处理】阀门状态更新完成，耗时: " + (valveUpdateEndTime - valveUpdateStartTime) + "ms");
            } else {
                System.out.println("【数据处理】没有阀门数据需要处理");
            }
            
            if (!heatMeterDataList.isEmpty()) {
                System.out.println("【数据处理】开始批量插入热表数据，数量: " + heatMeterDataList.size());
                long heatMeterInsertStartTime = System.currentTimeMillis();
                meterBatchInsertData(heatMeterDataList);
                long heatMeterInsertEndTime = System.currentTimeMillis();
                System.out.println("【数据处理】热表数据插入完成，耗时: " + (heatMeterInsertEndTime - heatMeterInsertStartTime) + "ms");
            } else {
                System.out.println("【数据处理】没有热表数据需要处理");
            }
            
        } finally {
            try {
                cursor.close();
                System.out.println("【数据处理】已关闭Redis游标");
            } catch (IOException e) {
                System.out.println("【数据处理】关闭Redis游标时出错: " + e.getMessage());
                throw new RuntimeException(e);
            }
        }
        
        //删除处理过的元素
        if (!itemsToRemove.isEmpty()) {
            System.out.println("【数据处理】开始从Redis中删除已处理的" + itemsToRemove.size() + "项数据...");
            long removeStartTime = System.currentTimeMillis();
            Object[] itemsArray = itemsToRemove.toArray();
            redisTemplate.opsForSet().remove(key, itemsArray);
            long removeEndTime = System.currentTimeMillis();
            System.out.println("【数据处理】已从Redis中删除已处理数据，耗时: " + (removeEndTime - removeStartTime) + "ms");
        }
        
        long endTime = System.currentTimeMillis();
        System.out.println("【数据处理】任务执行完成，总耗时: " + (endTime - startTime) + "ms");
    }

    //批量更新数据库 阀门状态
    public void batchUpdateData(List<HValvesData> valveDataList)
    {
        System.out.println("【阀门状态更新】开始批量更新阀门状态...");
        long startTime = System.currentTimeMillis();
        int batchSize = 500; // 设定一个较小的批次大小
        int totalUpdated = 0;
        
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false); // 显式开始事务，关闭自动提交
            String updateSql="update t_house set valve_status= ? ,add_code=?  where valves_No=?";
            
            try (PreparedStatement pstmt = connection.prepareStatement(updateSql))
            {
                int count = 0;
                System.out.println("【阀门状态更新】开始处理阀门数据列表...");
                for (HValvesData valveData : valveDataList)
                {
                    pstmt.setString(1, valveData.getFSVState());
                    pstmt.setString(2, valveData.getAddcode());
                    pstmt.setString(3, valveData.getValvesNo());
                    pstmt.addBatch();
                    if (++count % batchSize == 0) {
                        System.out.println("【阀门状态更新】执行批量更新，批次大小: " + count);
                        long batchStartTime = System.currentTimeMillis();
                        pstmt.executeBatch(); // 执行批处理
                        pstmt.clearBatch(); // 清空批处理
                        long batchEndTime = System.currentTimeMillis();
                        System.out.println("【阀门状态更新】批次执行完成，耗时: " + (batchEndTime - batchStartTime) + "ms");
                        totalUpdated += batchSize;
                    }
                }
                
                // 处理剩余的记录
                if (count % batchSize != 0) {
                    int remaining = count % batchSize;
                    System.out.println("【阀门状态更新】执行剩余批量更新，数量: " + remaining);
                    long batchStartTime = System.currentTimeMillis();
                    pstmt.executeBatch(); // 执行剩余的批处理
                    pstmt.clearBatch(); // 清空批处理
                    long batchEndTime = System.currentTimeMillis();
                    System.out.println("【阀门状态更新】剩余批次执行完成，耗时: " + (batchEndTime - batchStartTime) + "ms");
                    totalUpdated += remaining;
                }
                connection.commit(); // 提交事务
                
            }catch (Exception ex) {
                ex.printStackTrace();
                System.out.println("【阀门状态更新】更新过程中出错: " + ex.getMessage());
                connection.rollback();
            }
            
            long endTime = System.currentTimeMillis();
            System.out.println("【阀门状态更新】更新完成，共更新" + totalUpdated + "条记录，总耗时: " + (endTime - startTime) + "ms");
            
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("【阀门状态更新】处理过程中发生错误: " + ex.getMessage());
        }
    }

    //批量插入数据库 阀门数据
    public void batchInsertData(List<HValvesData> valveDataList)
    {
        long totalStartTime = System.currentTimeMillis();
        int totalInserted = 0;
        int batchSize = 500; // 设定一个较小的批次大小
        String hashname1 = "JM:ValvesData";//存储阀门实时数据
        //存放的集中器id 和 小区编号
        String hashname="JM:UnitNolist";
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false); // 开启事务
            // 遍历每个小区
            int processedUnits = 0;
            Map<String, Object> redisBatch = new HashMap<>();
            for (HeatUnit heatUnit : heatUnitlst)
            {
                long unitStartTime = System.currentTimeMillis();
                String tableName = "t_valves_data_" + heatUnit.getUnitNo();
                boolean tableExists = checkTableExists(connection, tableName);

                if (!tableExists) {
                    System.out.println("【阀门数据插入】表不存在: " + tableName);
                    continue; // 表不存在，跳过插入
                }

                System.out.println("【阀门数据插入】处理小区[" + heatUnit.getUnitNo() + "]，使用表：" + tableName);
                
                // 构建插入语句模板
                String insertSql= "INSERT INTO "+tableName+" (collectDt, valves_no, F_S_V, S_F_S_V, F_S_V_STATE,g_temperature,h_temperature) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?)";
                int unitInsertCount = 0;
                
                try (PreparedStatement pstmt = connection.prepareStatement(insertSql))
                {
                    int count = 0;
                    for (HValvesData valveData : valveDataList)
                    {
                        // 根据集中器id获取小区编号
                        String concentratorId = valveData.getConcentratorId();
                        Object unitno= redisUtils.hget(hashname,concentratorId);

                        if(unitno !=null)
                        {
                            if (heatUnit.getUnitNo().equals(String.valueOf(unitno)))
                            {
                                pstmt.setString(1, valveData.getCollectdt());
                                pstmt.setString(2, valveData.getValvesNo());
                                pstmt.setString(3, valveData.getFSV());
                                pstmt.setString(4, valveData.getSFSV());
                                pstmt.setString(5, valveData.getFSVState());
                                pstmt.setString(6, valveData.getGTemperature());
                                pstmt.setString(7, valveData.getHTemperature());
                                pstmt.addBatch();
                                unitInsertCount++;
                                if (++count % batchSize == 0) {
                                    System.out.println("【阀门数据插入】执行批量插入，批次大小: " + count);
                                    pstmt.executeBatch(); // 执行批处理
                                    pstmt.clearBatch(); // 清空批处理
                                    System.out.println("【阀门数据插入】批次执行完成");
                                    count=0;
                                }
                                //存储到redis 中
                                String value = String.join(",", unitno.toString(),
                                        valveData.getCollectdt(), valveData.getFSV(),
                                        valveData.getGTemperature(), valveData.getHTemperature());
                                redisBatch.put(valveData.getValvesNo(), value);
                            }
                        }
                    }
                    
                    // 执行剩余的批处理
                    if (count % batchSize != 0) {
                        System.out.println("【阀门数据插入】执行剩余批量插入，数量: " + (count % batchSize));
                        pstmt.executeBatch(); // 执行剩余的批处理
                        pstmt.clearBatch(); // 清空批处理
                    }
                    
                    totalInserted += unitInsertCount;
                    long unitEndTime = System.currentTimeMillis();
                    System.out.println("【阀门数据插入】小区[" + heatUnit.getUnitNo() + "]处理完成，插入: " + unitInsertCount + 
                            "条记录，耗时: " + (unitEndTime - unitStartTime) + "ms");
                    processedUnits++;
                    
                }catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("【阀门数据插入】小区[" + heatUnit.getUnitNo() + "]处理出错: " + ex.getMessage());
                    // 回滚事务
                    connection.rollback();
                    break; // 如果选择退出循环
                }
            }

            connection.commit();
            // 批量写入 Redis
            if (!redisBatch.isEmpty()) {
                redisUtils.hmset(hashname1,redisBatch);
            }

            long totalEndTime = System.currentTimeMillis();
            System.out.println("【阀门数据插入】批量插入完成，共处理" + processedUnits + "个小区，插入" + 
                    totalInserted + "条阀门数据，总耗时: " + (totalEndTime - totalStartTime) + "ms");
            
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("【阀门数据插入】处理过程中发生错误: " + ex.getMessage());
        }
    }

    private boolean checkTableExists(Connection connection, String tableName) throws SQLException {
        String schema = connection.getCatalog(); // 当前数据库名称
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, schema);
            ps.setString(2, tableName);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }
    //批量插入热表数据

    public void meterBatchInsertData(List<HeatMaterData> heatMeterDataList)
    {
        long totalStartTime = System.currentTimeMillis();
        int totalInserted = 0;
        int batchSize = 500; // 设定一个较小的批次大小
        String key = "JM:HeatMeterData"; // 列表键名
        Map<String, Object> redisBatch = new HashMap<>();
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false); // 开启事务
            // 遍历每个小区
            int processedUnits = 0;
            for (HeatUnit heatUnit : heatUnitlst)
            {
                String tableName = "t_heatmeter_data_" + heatUnit.getUnitNo();
                boolean tableExists = checkTableExists(connection, tableName);

                if (!tableExists) {
                    System.out.println("【热表数据插入】表不存在: " + tableName);
                    continue; // 表不存在，跳过插入
                }
                System.out.println("【热表数据插入】处理小区[" + heatUnit.getUnitNo() + "]，使用表：" + tableName);
                
                // 构建插入语句模板
                String insertSql= "INSERT INTO "+tableName+" (collectDt, heat_meter_no, F_S_C, F_S_H, power,velocity_flow,water_yield,F_S_T,F_B_T) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                int unitInsertCount = 0;
                
                try (PreparedStatement pstmt = connection.prepareStatement(insertSql))
                {
                    int count = 0;
                    for (HeatMaterData heatMaterData : heatMeterDataList)
                    {
                        // 根据集中器id获取小区编号
                        String concentratorId = heatMaterData.getConcentratorId();
                        //存放的集中器id 和 小区编号
                        String hashname="JM:UnitNolist";
                        Object unitno= redisUtils.hget(hashname,concentratorId);
                        if(unitno !=null)
                        {
                            if (heatUnit.getUnitNo().equals(String.valueOf(unitno)))
                            {
                                pstmt.setString(1, heatMaterData.getCollectdt());
                                pstmt.setString(2, heatMaterData.getHeatMeterNo());
                                pstmt.setString(3, heatMaterData.getFSC());
                                pstmt.setString(4, heatMaterData.getFSH());
                                pstmt.setString(5, heatMaterData.getPower());
                                pstmt.setString(6, heatMaterData.getVelocityFlow());
                                pstmt.setString(7, heatMaterData.getWaterYield());
                                pstmt.setString(8, heatMaterData.getFST());
                                pstmt.setString(9, heatMaterData.getFBT());
                                pstmt.addBatch();
                                unitInsertCount++;
                                if (++count % batchSize == 0) {
                                    System.out.println("【热表数据插入】执行批量插入，批次大小: " + count);
                                    long batchStartTime = System.currentTimeMillis();
                                    pstmt.executeBatch(); // 执行批处理
                                    pstmt.clearBatch(); // 清空批处理
                                    long batchEndTime = System.currentTimeMillis();
                                    System.out.println("【热表数据插入】批次执行完成，耗时: " + (batchEndTime - batchStartTime) + "ms");
                                }
                                //存储到redis 中

                                String value=unitno+","+heatMaterData.getHeatMeterNo()+","+heatMaterData.getCollectdt()+","
                                        +heatMaterData.getFSC()+","+heatMaterData.getFSH()+","+heatMaterData.getFST()+","
                                        +heatMaterData.getFBT()+","+heatMaterData.getPower();
                               // redisUtils.hset(key,heatMaterData.getHeatMeterNo(),value);
                                redisBatch.put(heatMaterData.getHeatMeterNo(), value);
                            }
                        }
                    }
                    // 执行剩余的批处理
                    if (count % batchSize != 0) {
                        pstmt.executeBatch(); // 执行剩余的批处理
                        pstmt.clearBatch(); // 清空批处理
                    }
                    totalInserted += unitInsertCount;
                    processedUnits++;
                    
                }catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("【热表数据插入】小区[" + heatUnit.getUnitNo() + "]处理出错: " + ex.getMessage());
                    // 回滚事务
                    connection.rollback();
                    // 可以选择继续循环处理后续数据或者直接退出循环
                    break; // 如果选择退出循环
                }
            }
            // 提交事务
            connection.commit();
            // 批量写入 Redis
            if (!redisBatch.isEmpty()) {
                redisUtils.hmset(key,redisBatch);
            }

            long totalEndTime = System.currentTimeMillis();
            System.out.println("【热表数据插入】批量插入完成，共处理" + processedUnits + "个小区，插入" + 
                    totalInserted + "条热表数据，总耗时: " + (totalEndTime - totalStartTime) + "ms");
            
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("【热表数据插入】处理过程中发生错误: " + ex.getMessage());
        }
    }

    //解析阀门数据
    private  HValvesData  parseHValvesData(String concentratorId,String valvesno,String dataString,Integer cycle) {
       // System.out.println("【阀门数据解析】开始解析阀门数据[" + valvesno + "]...");
        //long startTime = System.currentTimeMillis();

        try {
            byte[] data = CalcUtils.hexStringToByteArray(dataString);
            // 检查数据长度是否符合预期
            if (data.length < 95) {
                System.out.println("【阀门数据解析】数据长度不符合预期，无法解析，阀门号: " + valvesno +
                                 ", 集中器ID: " + concentratorId + ", 数据长度: " + data.length +
                                 ", 期望长度: >=95, 原始数据: " + dataString);
                return null;
            }

           // System.out.println("【阀门数据解析】提取阀门各项参数...");
            // 解析每个字段
            String addressCode = CalcUtils.extractAndConvertBytesToHex(data, 2, 7);//功能码
            //System.out.println(addressCode);

            // 提取时间数据并添加验证
            String systemTimeHex = CalcUtils.extractAndConvertBytesToHexReversed(data, 13, 7);
            String systemTime;

            try {
                // 验证时间字符串长度
                if (systemTimeHex == null || systemTimeHex.length() != 14) {
                    System.out.println("【阀门数据解析】时间数据格式错误，阀门号: " + valvesno +
                                     ", 时间十六进制: " + systemTimeHex + ", 长度: " +
                                     (systemTimeHex != null ? systemTimeHex.length() : "null"));
                    return null;
                }

                systemTime = StringUtils.parseTime(systemTimeHex);
            } catch (Exception timeEx) {
                System.out.println("【阀门数据解析】时间解析失败，阀门号: " + valvesno +
                                 ", 时间十六进制: " + systemTimeHex + ", 错误: " + timeEx.getMessage());
                return null;
            }

            int deviceStatus = data[21] & 0xFF;
            int valveOening = data[22] & 0xFF;
            int setOpeningp = data[23] & 0xFF;
            //String returnWaterTemperature = extractAndConvertBytesToHexReversed(data, 26, 2);

            Float hWaterTemperature = (float)Integer.parseInt(extractAndConvertBytesToHexReversed(data, 26, 2),16)/100;
            Float gWaterTemperature = (float)Integer.parseInt(extractAndConvertBytesToHexReversed(data, 24, 2),16)/100;

            //调整时间
            //System.out.println("【阀门数据解析】调整时间到最近的间隔...");
            String time=roundToNearestInterval(systemTime,cycle);
           // System.out.println("【阀门数据解析】阀门[" + valvesno + "]源时间=" + systemTime + "，调整后时间=" + time);

            HValvesData hvalvesData = new HValvesData();
            hvalvesData.setCollectdt(time);//系统时间
            hvalvesData.setConcentratorId(concentratorId);//集中器id`
            hvalvesData.setValvesNo(valvesno);//仪表id
            hvalvesData.setFSV(String.valueOf(valveOening));//阀门开度
            hvalvesData.setSFSV(String.valueOf(setOpeningp));//设定开度           00000000   128  00
            hvalvesData.setFSVState(String.valueOf(deviceStatus));//阀门状态 00   01234567
            hvalvesData.setAddcode(addressCode);//功能码
            hvalvesData.setHTemperature(String.valueOf(hWaterTemperature));//回水温度
            hvalvesData.setGTemperature(String.valueOf(gWaterTemperature));//供水温度

            //long endTime = System.currentTimeMillis();
           // System.out.println("【阀门数据解析】阀门[" + valvesno + "]解析完成，状态: " + deviceStatus +
                    //"，开度: " + valveOening + "，供水温度: " + gWaterTemperature +
                   // "，回水温度: " + hWaterTemperature + "，耗时: " + (endTime - startTime) + "ms");

            return hvalvesData;

        } catch (Exception ex) {
            System.out.println("【阀门数据解析】解析过程中发生异常，阀门号: " + valvesno +
                             ", 集中器ID: " + concentratorId + ", 错误: " + ex.getMessage());
            ex.printStackTrace();
            return null;
        }
    }

    //解析热表数据
    private  HeatMaterData  parseHeatData(String concentratorId,String heatmeterno,String dataString,Integer cycle) {
        System.out.println("【热表数据解析】开始解析热表数据[" + heatmeterno + "]...");
        long startTime = System.currentTimeMillis();
        
        byte[] data = CalcUtils.hexStringToByteArray(dataString);
        if (data.length < 59) {
            System.out.println("【热表数据解析】数据长度不符合预期，无法解析，长度: " + data.length);
            return null;
        }
        
        System.out.println("【热表数据解析】提取热表各项参数...");
        int coolingCapacity = Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 15, 4));
        int heat = Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 20, 4));
        int power = Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 25, 4));
        int velocityFlow = Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 30, 4));
        int waterYield = Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 35, 4));
        Float FST = (float) Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 39, 3)) / 100;
        Float FBT = (float) Integer.parseInt(CalcUtils.extractAndConvertBytesToHexReversed(data, 42, 3)) / 100;
        String systemTime = CalcUtils.extractAndConvertBytesToHexReversed(data, 48, 7);
        systemTime = StringUtils.parseTime(systemTime);
        
        //调整时间
        System.out.println("【热表数据解析】调整时间到最近的间隔...");
        String time=roundToNearestInterval(systemTime,cycle);
        System.out.println("【热表数据解析】热表[" + heatmeterno + "]源时间=" + systemTime + "，调整后时间=" + time);

        HeatMaterData heatMaterData = new HeatMaterData();
        heatMaterData.setCollectdt(time);//系统时间
        heatMaterData.setHeatMeterNo(heatmeterno);//热表id
        heatMaterData.setFSC(String.valueOf(coolingCapacity));//冷量
        heatMaterData.setFSH(String.valueOf(heat));//热量
        heatMaterData.setPower(String.valueOf(power));//功率
        heatMaterData.setVelocityFlow(String.valueOf(velocityFlow));//流速
        heatMaterData.setWaterYield(String.valueOf(waterYield));//水量
        heatMaterData.setFST(String.valueOf(FST));//进水温度
        heatMaterData.setFBT(String.valueOf(FBT));//回水温度
        heatMaterData.setConcentratorId(concentratorId);//集中器id
        
        long endTime = System.currentTimeMillis();
        System.out.println("【热表数据解析】热表[" + heatmeterno + "]解析完成，热量: " + heat + 
                "，功率: " + power + "，进水温度: " + FST + "，回水温度: " + FBT + 
                "，耗时: " + (endTime - startTime) + "ms");
        
        return heatMaterData;
    }

    //插入数据日志
    public void insertLog(String status,String data) {
        String sql = "INSERT INTO t_data_log (status,data, dt) VALUES (?, ?, ?)";
        String dt= getCurrentDateTime();
        // 使用 try-with-resources 确保资源自动关闭
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(sql)) {

            // 设置 PreparedStatement 参数
            pstmt.setString(1, status);
            pstmt.setString(2, data);
            pstmt.setString(3, dt);
            // 执行插入操作
            pstmt.executeUpdate();

        } catch (SQLException e) {
            // 处理 SQL 异常
            e.printStackTrace();
            // 可以根据需要选择抛出异常或进行其他处理
            throw new RuntimeException("Failed to insert log into database", e);
        }
    }

    //获取阀门热表采集周期
    public Integer getCycleByDataBase() {
        System.out.println("【获取采集周期】开始从数据库获取采集周期参数...");
        String sql = "select collect_cycle from t_other_param where id=1";
        
        Integer cycle = 30; // 默认值设为30
        // 使用 try-with-resources 确保资源自动关闭
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(sql)) {
            // 执行查询操作
            ResultSet rSet = pstmt.executeQuery();
            if (rSet.next()) {
                cycle = rSet.getInt("collect_cycle");
                System.out.println("【获取采集周期】成功从数据库读取采集周期: " + cycle);
            } else {
                System.out.println("【获取采集周期】未找到采集周期配置，使用默认值: " + cycle);
            }
            
        } catch (SQLException e) {
            // 处理 SQL 异常
            e.printStackTrace();
            System.out.println("【获取采集周期】获取采集周期出错: " + e.getMessage() + "，使用默认值: " + cycle);
            // 可以根据需要选择抛出异常或进行其他处理
            throw new RuntimeException("Failed to get cycle from database", e);
        }
        return cycle;
    }

    // 工具方法：将字节数组转换为整数
    private static int[] parseByteArrayToIntArray(byte[] bytes, int start, int length) {
        int[] intArray = new int[length];
        for (int i = 0; i < length; i++) {
            intArray[i] = bytes[start + i] & 0xFF;
        }
        return intArray;
    }

    //将字节数组转换为16进制并字节反转
    private static String extractAndConvertBytesToHexReversed(byte[] data, int start, int length) {
        byte[] reversed = new byte[length];
        for (int i = 0; i < length; i++) {
            reversed[length - i - 1] = data[start + i];
        }
        StringBuilder sb = new StringBuilder(length * 2);
        for (byte b : reversed) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }


    //interval参数允许用户指定舍入的分钟间隔。
    public static String roundToNearestInterval(String time, int interval) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);

        // Get the number of minutes past the hour
        int minutes = dateTime.getMinute();
        int seconds = dateTime.getSecond();
        int nanos = dateTime.getNano();

        // Calculate the nearest interval
        int remainder = minutes % interval;
        int roundedMinutes = minutes - remainder;

        // If rounding down would result in a negative number of minutes, subtract an interval from the hour
        if (roundedMinutes < 0) {
            roundedMinutes += interval;
        }

        // Adjust the hour if necessary and set seconds and nanos to zero
        if (remainder != 0 || seconds != 0 || nanos != 0) {
            // Check if we need to roll over to the next hour
            boolean rollOver = roundedMinutes >= 60;
            if (rollOver) {
                roundedMinutes -= 60;
            }

            dateTime = dateTime.withMinute(roundedMinutes)
                    .withSecond(0)
                    .withNano(0);

            // If rolling over, adjust the hour
            if (rollOver) {
                dateTime = dateTime.plusHours(1);
            }
        } else {
            // If already at the start of an interval but with non-zero seconds or nanos, reset them
            dateTime = dateTime.withSecond(0).withNano(0);
        }

        // Format back to a string
        return dateTime.format(formatter);
    }

    //将时间舍入到最接近的半小时
    public static String roundToNearestHalfHour(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);

        // Get the number of minutes past the hour
        int minutes = dateTime.getMinute();

        // Determine if we should round to 00 or 30
        if (minutes < 30) {
            // Round down to the current hour's 00 minutes
            dateTime = dateTime.withMinute(0).withSecond(0).withNano(0);
        } else {
            // Round up to the current hour's 30 minutes
            dateTime = dateTime.withMinute(30).withSecond(0).withNano(0);
        }

        // Format back to a string
        return dateTime.format(formatter);
    }



    private String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        String formattedDateTime = sdf.format(System.currentTimeMillis());
        return formattedDateTime;
    }

}




