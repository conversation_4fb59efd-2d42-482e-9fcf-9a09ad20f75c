package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.Hes;
import com.javaweb.system.entity.HesYearHArea;
import com.javaweb.system.entity.User;
import com.javaweb.system.mapper.HesMapper;
import com.javaweb.system.mapper.HesYearHAreaMapper;
import com.javaweb.system.mapper.HouseInfoMapper;
import com.javaweb.system.mapper.UserMapper;
import com.javaweb.system.query.HesYearHAreaQuery;
import com.javaweb.system.service.IHesYearHAreaService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 15:27
 */
@Service
public class HesYearHAreaServiceImpl extends ServiceImpl<HesYearHAreaMapper, HesYearHArea> implements IHesYearHAreaService {

    @Autowired
    HesYearHAreaMapper hesYearHAreaMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private HesMapper thesMapper;

    @Autowired
    private HouseInfoMapper houseInfoMapper;

    @Override
    public JsonResult getList(BaseQuery query) {
        HesYearHAreaQuery hesYearHAreaQuery = (HesYearHAreaQuery ) query;

        QueryWrapper<HesYearHArea> queryWrapper = new QueryWrapper<>();

        if (!StringUtils.isEmpty(hesYearHAreaQuery.getHesname())) {
            queryWrapper.like("hesname", hesYearHAreaQuery.getHesname());
        }
        //得到项目权限
        int userId= ShiroUtils.getUserId();
        User user = userMapper.selectById(userId);

        if(!user.getHeatUnitId().equals("0"))
        {
            // 使用StringUtils.split和Arrays.stream来转换字符串到int数组
            int[] intArray = Arrays.stream(StringUtils.split(user.getHeatUnitId(), ','))
                    .mapToInt(Integer::parseInt)
                    .toArray();
            //根据小区权限获得小区对应的多有换热站编号
            List<Integer> hescodeLst=thesMapper.getHesCodesByHeatunitId(intArray);
            queryWrapper.in("hescode", hescodeLst);
        }

        queryWrapper.orderByDesc("id");
        Integer THpage = 1;
        Integer THlimit = 10;
        if (hesYearHAreaQuery.getPage()!=null){
            THpage = hesYearHAreaQuery.getPage();
        }
        if (hesYearHAreaQuery.getLimit()!=null){
            THlimit =hesYearHAreaQuery.getLimit();
        }

        IPage<HesYearHArea> page = new Page<>(THpage, THlimit);
        IPage<HesYearHArea> page1 = hesYearHAreaMapper.selectPage(page, queryWrapper);
        return JsonResult.success(page1);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HesYearHArea entity) {
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHareaList() {
        QueryWrapper<HesYearHArea> queryWrapper =new QueryWrapper<>();
        //得到项目权限
        int userId= ShiroUtils.getUserId();
        User user = userMapper.selectById(userId);

        if(!user.getHeatUnitId().equals("0"))
        {
            // 使用StringUtils.split和Arrays.stream来转换字符串到int数组
            int[] intArray = Arrays.stream(StringUtils.split(user.getHeatUnitId(), ','))
                    .mapToInt(Integer::parseInt)
                    .toArray();
            //根据小区权限获得小区对应的多有换热站编号
            List<Integer> hescodeLst=thesMapper.getHesCodesByHeatunitId(intArray);
            queryWrapper.in("hescode", hescodeLst);
        }
        List<HesYearHArea> hareaList = list(queryWrapper);
        return JsonResult.success(hareaList);
    }

    /**
     * 计算当年每个换热站的供热面积
     * 遍历所有换热站，根据关联的小区ID查询供暖状态住户的面积总和
     * 插入或更新换热站年度供热面积记录
     *
     * @return 计算结果，包含处理的换热站数量和详细信息
     */
    @Override
    public JsonResult calculateCurrentYearHeatingArea() {
        try {
            // 获取当前年份
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);

            // 获取所有换热站信息（包含关联的小区ID）
            List<Hes> hesList = thesMapper.getAllHesWithHeatUnitId();

            if (hesList == null || hesList.isEmpty()) {
                return JsonResult.error("没有找到关联小区的换热站信息");
            }

            List<Map<String, Object>> processResults = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            DecimalFormat df = new DecimalFormat("#.##");

            // 遍历所有换热站
            for (Hes hes : hesList) {
                try {
                    Map<String, Object> hesResult = new HashMap<>();
                    hesResult.put("hescode", hes.getHescode());
                    hesResult.put("hesname", hes.getName());
                    hesResult.put("heatUnitId", hes.getHeatUnitId());

                    // 根据小区ID查询供暖状态住户的面积总和
                    Double heatingArea = houseInfoMapper.getTotalHeatingAreaByHeatUnitId(hes.getHeatUnitId());
                    if (heatingArea == null) {
                        heatingArea = 0.0;
                    }

                    // 查询非供暖状态住户的面积总和
                    Double nonHeatingArea = houseInfoMapper.getTotalNonHeatingAreaByHeatUnitId(hes.getHeatUnitId());
                    if (nonHeatingArea == null) {
                        nonHeatingArea = 0.0;
                    }

                    // 格式化面积数据，保留两位小数
                    String heatingAreaStr = df.format(heatingArea);
                    String nonHeatingAreaStr = df.format(nonHeatingArea);

                    hesResult.put("heatingArea", heatingAreaStr);
                    hesResult.put("nonHeatingArea", nonHeatingAreaStr);

                    // 检查当年记录是否存在
                    Integer recordCount = hesYearHAreaMapper.checkRecordExists(hes.getHescode(), currentYear);

                    if (recordCount != null && recordCount > 0) {
                        // 更新现有记录
                        hesYearHAreaMapper.updateHesYearHArea(hes.getHescode(),hes.getName(), currentYear,
                                                            heatingAreaStr, nonHeatingAreaStr);
//                        hesResult.put("operation", "更新");
                    } else {
                        // 插入新记录
                        HesYearHArea newRecord = new HesYearHArea();
                        newRecord.setHesname(hes.getName());
                        newRecord.setHescode(hes.getHescode());
                        newRecord.setHyear(currentYear);
                        newRecord.setHeara(heatingAreaStr);
                        newRecord.setFreearea(nonHeatingAreaStr);

                        hesYearHAreaMapper.insertHesYearHArea(newRecord);
//                        hesResult.put("operation", "插入");
                    }

                    hesResult.put("status", "成功");
                    processResults.add(hesResult);
                    successCount++;

                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("hescode", hes.getHescode());
                    errorResult.put("hesname", hes.getName());
                    errorResult.put("status", "失败");
                    errorResult.put("error", e.getMessage());
                    processResults.add(errorResult);
                    errorCount++;

                    e.printStackTrace();
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("year", currentYear);
            result.put("totalHesCount", hesList.size());
            result.put("successCount", successCount);
            result.put("errorCount", errorCount);
            result.put("processTime", new Date());
            result.put("details", processResults);

            String message = String.format("计算完成！共处理%d个换热站，成功%d个，失败%d个",
                                          hesList.size(), successCount, errorCount);

            return JsonResult.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.error("计算换热站年度供热面积时发生错误：" + e.getMessage());
        }
    }

}

