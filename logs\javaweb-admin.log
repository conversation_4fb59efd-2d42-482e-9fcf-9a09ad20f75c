2025-08-20 08:56:07.398 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 09:01:21.629 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 09:59:54.529 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 10:02:41.028 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 10:03:58.124 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 10:07:03.796 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 10:08:31.224 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 10:48:44.536 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 11:05:56.173 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 11:06:23.855 [schedule-pool-3] ERROR com.javaweb.common.utils.ThreadUtils - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy65.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:58)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy223.insertOperlog(Unknown Source)
	at com.javaweb.system.service.impl.OperLogServiceImpl.insertOperlog(OperLogServiceImpl.java:73)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$ef6c1a82.insertOperlog(<generated>)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$738946b4.insertOperlog(<generated>)
	at com.javaweb.system.manager.AsyncFactory$2.run(AsyncFactory.java:110)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy296.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 23 common frames omitted
2025-08-20 11:07:11.687 [schedule-pool-2] ERROR com.javaweb.common.utils.ThreadUtils - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy65.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:58)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy223.insertOperlog(Unknown Source)
	at com.javaweb.system.service.impl.OperLogServiceImpl.insertOperlog(OperLogServiceImpl.java:73)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$ef6c1a82.insertOperlog(<generated>)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$738946b4.insertOperlog(<generated>)
	at com.javaweb.system.manager.AsyncFactory$2.run(AsyncFactory.java:110)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy296.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 23 common frames omitted
2025-08-20 11:08:35.723 [schedule-pool-5] ERROR com.javaweb.common.utils.ThreadUtils - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy65.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:58)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy223.insertOperlog(Unknown Source)
	at com.javaweb.system.service.impl.OperLogServiceImpl.insertOperlog(OperLogServiceImpl.java:73)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$ef6c1a82.insertOperlog(<generated>)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$738946b4.insertOperlog(<generated>)
	at com.javaweb.system.manager.AsyncFactory$2.run(AsyncFactory.java:110)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy296.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 23 common frames omitted
2025-08-20 11:09:48.620 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 11:14:20.494 [schedule-pool-3] ERROR com.javaweb.common.utils.ThreadUtils - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
### The error may exist in file [E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\tb_jieming_gongre\javaweb-system\target\classes\mapper\OperLogMapper.xml]
### The error may involve com.javaweb.system.mapper.OperLogMapper.insertOperlog-Inline
### The error occurred while setting parameters
### SQL: insert into t_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)         values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate())
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
; Data truncation: Data too long for column 'json_result' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy65.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:58)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy223.insertOperlog(Unknown Source)
	at com.javaweb.system.service.impl.OperLogServiceImpl.insertOperlog(OperLogServiceImpl.java:73)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$73229021.insertOperlog(<generated>)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$FastClassBySpringCGLIB$$882be4be.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.javaweb.system.service.impl.OperLogServiceImpl$$EnhancerBySpringCGLIB$$f73fbc53.insertOperlog(<generated>)
	at com.javaweb.system.manager.AsyncFactory$2.run(AsyncFactory.java:110)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'json_result' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy296.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 23 common frames omitted
2025-08-20 11:18:40.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
2025-08-20 11:25:29.385 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.javaweb.system.mapper.UserMapper.getUserInfo] is ignored, because it exists, maybe from xml file
