package com.javaweb.api.workthread;

import com.javaweb.api.entity.HValvesData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;

/**
 * WorkThreadServer测试类
 * 主要测试阀门数据解析功能，特别是数据长度验证
 */
public class WorkThreadServerTest {

    private WorkThreadServer workThreadServer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        workThreadServer = new WorkThreadServer();
    }

    /**
     * 测试数据长度小于95字节的情况
     * 期望：返回null，并输出错误日志
     */
    @Test
    void testParseHValvesData_DataLengthLessThan95() {
        // 准备测试数据 - 创建一个长度为94字节的16进制字符串（188个字符）
        StringBuilder shortDataBuilder = new StringBuilder();
        for (int i = 0; i < 94; i++) {
            shortDataBuilder.append("FF");
        }
        String shortDataString = shortDataBuilder.toString();
        
        // 调用方法
        HValvesData result = workThreadServer.parseHValvesData("TEST001", "VALVE001", shortDataString, 30);
        
        // 验证结果
        assertNull(result, "数据长度小于95字节时应返回null");
    }

    /**
     * 测试数据长度等于95字节但时间数据无效的情况
     * 期望：返回null，并输出时间解析错误日志
     */
    @Test
    void testParseHValvesData_InvalidTimeData() {
        // 准备测试数据 - 创建一个长度为95字节的16进制字符串，但时间部分无效
        StringBuilder dataBuilder = new StringBuilder();
        
        // 前13个字节
        for (int i = 0; i < 13; i++) {
            dataBuilder.append("00");
        }
        
        // 时间部分（7个字节）- 设置为无效的时间数据
        dataBuilder.append("FF404040404040"); // 这会导致日期解析为40，超出有效范围
        
        // 剩余字节
        for (int i = 20; i < 95; i++) {
            dataBuilder.append("00");
        }
        
        String invalidTimeDataString = dataBuilder.toString();
        
        // 调用方法
        HValvesData result = workThreadServer.parseHValvesData("TEST002", "VALVE002", invalidTimeDataString, 30);
        
        // 验证结果
        assertNull(result, "时间数据无效时应返回null");
    }

    /**
     * 测试正常的数据解析情况
     * 期望：返回有效的HValvesData对象
     */
    @Test
    void testParseHValvesData_ValidData() {
        // 准备测试数据 - 创建一个长度为95字节的有效16进制字符串
        StringBuilder dataBuilder = new StringBuilder();
        
        // 前2个字节
        dataBuilder.append("0000");
        
        // 功能码部分（7个字节）
        dataBuilder.append("01020304050607");
        
        // 时间部分（7个字节）- 有效时间：2024年11月04日16时00分13秒
        // 反转后的字节序列：13 00 16 04 11 20 24
        dataBuilder.append("0D001004112024");
        
        // 设备状态、阀门开度、设定开度
        dataBuilder.append("010050"); // 位置21,22,23
        
        // 供水温度（2字节）
        dataBuilder.append("1234"); // 位置24,25
        
        // 回水温度（2字节）  
        dataBuilder.append("5678"); // 位置26,27
        
        // 填充剩余字节到95字节
        for (int i = 28; i < 95; i++) {
            dataBuilder.append("00");
        }
        
        String validDataString = dataBuilder.toString();
        
        // 调用方法
        HValvesData result = workThreadServer.parseHValvesData("TEST003", "VALVE003", validDataString, 30);
        
        // 验证结果
        assertNotNull(result, "有效数据应返回非null的HValvesData对象");
        assertEquals("TEST003", result.getConcentratorId());
        assertEquals("VALVE003", result.getValvesNo());
        assertEquals("80", result.getFSV()); // 0x50 = 80
        assertEquals("1", result.getFSVState()); // 设备状态
    }

    /**
     * 测试空数据字符串的情况
     * 期望：返回null，并输出异常日志
     */
    @Test
    void testParseHValvesData_EmptyDataString() {
        // 调用方法
        HValvesData result = workThreadServer.parseHValvesData("TEST004", "VALVE004", "", 30);
        
        // 验证结果
        assertNull(result, "空数据字符串应返回null");
    }

    /**
     * 测试null数据字符串的情况
     * 期望：返回null，并输出异常日志
     */
    @Test
    void testParseHValvesData_NullDataString() {
        // 调用方法
        HValvesData result = workThreadServer.parseHValvesData("TEST005", "VALVE005", null, 30);
        
        // 验证结果
        assertNull(result, "null数据字符串应返回null");
    }
}
